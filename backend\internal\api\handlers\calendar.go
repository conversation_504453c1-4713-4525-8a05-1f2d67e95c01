package handlers

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// DocumentCalendarItem represents a document in calendar context
type DocumentCalendarItem struct {
	ID                   uint       `json:"id"`
	Title                string     `json:"title"`
	Slug                 string     `json:"slug"`
	Type                 string     `json:"type"`
	Status               string     `json:"status"`
	PublicationDate      *time.Time `json:"publication_date"`
	EffectiveDate        *time.Time `json:"effective_date"`
	TerminationDate      *time.Time `json:"termination_date"`
	CommentDueDate       *time.Time `json:"comment_due_date"`
	FRDocumentNumber     string     `json:"fr_document_number"`
	DocketNumber         string     `json:"docket_number"`
	Agency               gin.H      `json:"agency"`
	DaysUntilEffective   int        `json:"days_until_effective,omitempty"`
	DaysUntilTermination int        `json:"days_until_termination,omitempty"`
}

// CalendarResponse represents the calendar data structure
type CalendarResponse struct {
	Documents gin.H   `json:"documents"`
	Events    []gin.H `json:"events"`
	View      string  `json:"view"`
	DateRange gin.H   `json:"date_range"`
	Filters   gin.H   `json:"filters"`
}

// GetCalendar returns document effectiveness calendar data
func GetCalendar(c *gin.Context) {
	// Parse query parameters
	fromDate := c.Query("from")
	toDate := c.Query("to")
	agencyID := c.Query("agency_id")
	docType := c.Query("type")
	view := c.DefaultQuery("view", "month")

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Parse date filters
	var fromTime, toTime *time.Time
	if fromDate != "" {
		if parsed, err := time.Parse("2006-01-02", fromDate); err == nil {
			fromTime = &parsed
		}
	}
	if toDate != "" {
		if parsed, err := time.Parse("2006-01-02", toDate); err == nil {
			toTime = &parsed
		}
	}

	// Set default date range if not provided
	now := time.Now()
	if fromTime == nil {
		start := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		fromTime = &start
		fromDate = start.Format("2006-01-02")
	}
	if toTime == nil {
		end := fromTime.AddDate(0, 1, -1)
		toTime = &end
		toDate = end.Format("2006-01-02")
	}

	// Build base query for public documents
	baseQuery := db.Model(&models.Document{}).
		Preload("Agency").
		Where("is_public = ? AND visibility_level = ?", true, 1)

	// Apply filters
	if agencyID != "" {
		baseQuery = baseQuery.Where("agency_id = ?", agencyID)
	}
	if docType != "" {
		baseQuery = baseQuery.Where("type = ?", docType)
	}

	// Get documents that are not yet effective
	var notYetEffectiveDocs []models.Document
	notYetEffectiveQuery := baseQuery.Where("effective_date IS NOT NULL AND effective_date > NOW()")
	if fromTime != nil {
		notYetEffectiveQuery = notYetEffectiveQuery.Where("effective_date >= ?", fromTime)
	}
	if toTime != nil {
		notYetEffectiveQuery = notYetEffectiveQuery.Where("effective_date <= ?", toTime)
	}
	notYetEffectiveQuery.Order("effective_date ASC").Find(&notYetEffectiveDocs)

	// Get documents that are currently effective
	var effectiveDocs []models.Document
	effectiveQuery := baseQuery.Where("effective_date IS NOT NULL AND effective_date <= NOW() AND (termination_date IS NULL OR termination_date > NOW())")
	if fromTime != nil {
		effectiveQuery = effectiveQuery.Where("effective_date >= ?", fromTime)
	}
	if toTime != nil {
		effectiveQuery = effectiveQuery.Where("effective_date <= ?", toTime)
	}
	effectiveQuery.Order("effective_date DESC").Find(&effectiveDocs)

	// Get documents that are terminated
	var terminatedDocs []models.Document
	terminatedQuery := baseQuery.Where("termination_date IS NOT NULL AND termination_date <= NOW()")
	if fromTime != nil {
		terminatedQuery = terminatedQuery.Where("termination_date >= ?", fromTime)
	}
	if toTime != nil {
		terminatedQuery = terminatedQuery.Where("termination_date <= ?", toTime)
	}
	terminatedQuery.Order("termination_date DESC").Find(&terminatedDocs)

	// Convert to response format
	notYetEffective := make([]DocumentCalendarItem, len(notYetEffectiveDocs))
	for i, doc := range notYetEffectiveDocs {
		item := DocumentCalendarItem{
			ID:               doc.ID,
			Title:            doc.Title,
			Slug:             doc.Slug,
			Type:             string(doc.Type),
			Status:           string(doc.Status),
			PublicationDate:  doc.PublicationDate,
			EffectiveDate:    doc.EffectiveDate,
			TerminationDate:  doc.TerminationDate,
			CommentDueDate:   doc.CommentDueDate,
			FRDocumentNumber: doc.FRDocumentNumber,
			DocketNumber:     doc.DocketNumber,
		}

		if doc.Agency.ID != 0 {
			item.Agency = gin.H{
				"id":         doc.Agency.ID,
				"name":       doc.Agency.Name,
				"short_name": doc.Agency.ShortName,
				"slug":       doc.Agency.Slug,
			}
		}

		// Calculate days until effective
		if doc.EffectiveDate != nil {
			days := int(doc.EffectiveDate.Sub(now).Hours() / 24)
			if days >= 0 {
				item.DaysUntilEffective = days
			}
		}

		notYetEffective[i] = item
	}

	effective := make([]DocumentCalendarItem, len(effectiveDocs))
	for i, doc := range effectiveDocs {
		item := DocumentCalendarItem{
			ID:               doc.ID,
			Title:            doc.Title,
			Slug:             doc.Slug,
			Type:             string(doc.Type),
			Status:           string(doc.Status),
			PublicationDate:  doc.PublicationDate,
			EffectiveDate:    doc.EffectiveDate,
			TerminationDate:  doc.TerminationDate,
			CommentDueDate:   doc.CommentDueDate,
			FRDocumentNumber: doc.FRDocumentNumber,
			DocketNumber:     doc.DocketNumber,
		}

		if doc.Agency.ID != 0 {
			item.Agency = gin.H{
				"id":         doc.Agency.ID,
				"name":       doc.Agency.Name,
				"short_name": doc.Agency.ShortName,
				"slug":       doc.Agency.Slug,
			}
		}

		// Calculate days until termination
		if doc.TerminationDate != nil {
			days := int(doc.TerminationDate.Sub(now).Hours() / 24)
			if days >= 0 {
				item.DaysUntilTermination = days
			}
		}

		effective[i] = item
	}

	terminated := make([]DocumentCalendarItem, len(terminatedDocs))
	for i, doc := range terminatedDocs {
		item := DocumentCalendarItem{
			ID:               doc.ID,
			Title:            doc.Title,
			Slug:             doc.Slug,
			Type:             string(doc.Type),
			Status:           string(doc.Status),
			PublicationDate:  doc.PublicationDate,
			EffectiveDate:    doc.EffectiveDate,
			TerminationDate:  doc.TerminationDate,
			CommentDueDate:   doc.CommentDueDate,
			FRDocumentNumber: doc.FRDocumentNumber,
			DocketNumber:     doc.DocketNumber,
		}

		if doc.Agency.ID != 0 {
			item.Agency = gin.H{
				"id":         doc.Agency.ID,
				"name":       doc.Agency.Name,
				"short_name": doc.Agency.ShortName,
				"slug":       doc.Agency.Slug,
			}
		}

		terminated[i] = item
	}

	// Get calendar events from documents with public hearings and comment deadlines
	var events []gin.H

	// Get upcoming public hearings
	var hearingDocuments []models.Document
	hearingQuery := db.Model(&models.Document{}).
		Preload("Agency").
		Where("is_public = ? AND visibility_level = ? AND public_hearing_date IS NOT NULL AND public_hearing_date > NOW()", true, 1)

	if fromTime != nil {
		hearingQuery = hearingQuery.Where("public_hearing_date >= ?", fromTime)
	}
	if toTime != nil {
		hearingQuery = hearingQuery.Where("public_hearing_date <= ?", toTime)
	}
	if agencyID != "" {
		hearingQuery = hearingQuery.Where("agency_id = ?", agencyID)
	}

	hearingQuery.Find(&hearingDocuments)

	// Add public hearing events
	for _, doc := range hearingDocuments {
		if doc.PublicHearingDate != nil {
			events = append(events, gin.H{
				"id":          fmt.Sprintf("hearing_%d", doc.ID),
				"title":       fmt.Sprintf("Public Hearing - %s", doc.Title),
				"date":        doc.PublicHearingDate.Format("2006-01-02"),
				"time":        doc.PublicHearingDate.Format("15:04"),
				"type":        "hearing",
				"agency":      doc.Agency.ShortName,
				"location":    doc.PublicHearingInfo,
				"description": fmt.Sprintf("Public hearing for %s", doc.Title),
				"document_id": doc.ID,
			})
		}
	}

	// Get upcoming comment deadlines
	var commentDocuments []models.Document
	commentQuery := db.Model(&models.Document{}).
		Preload("Agency").
		Where("is_public = ? AND visibility_level = ? AND comment_due_date IS NOT NULL AND comment_due_date > NOW()", true, 1)

	if fromTime != nil {
		commentQuery = commentQuery.Where("comment_due_date >= ?", fromTime)
	}
	if toTime != nil {
		commentQuery = commentQuery.Where("comment_due_date <= ?", toTime)
	}
	if agencyID != "" {
		commentQuery = commentQuery.Where("agency_id = ?", agencyID)
	}

	commentQuery.Find(&commentDocuments)

	// Add comment deadline events
	for _, doc := range commentDocuments {
		if doc.CommentDueDate != nil {
			events = append(events, gin.H{
				"id":          fmt.Sprintf("deadline_%d", doc.ID),
				"title":       fmt.Sprintf("Comment Period Ends - %s", doc.Title),
				"date":        doc.CommentDueDate.Format("2006-01-02"),
				"time":        "23:59",
				"type":        "deadline",
				"agency":      doc.Agency.ShortName,
				"description": fmt.Sprintf("Final day to submit comments on %s", doc.Title),
				"document_id": doc.ID,
			})
		}
	}

	// Get manually created calendar events (tasks with source_type = "manual")
	var manualTasks []models.Task
	taskQuery := db.Model(&models.Task{}).
		Preload("Agency").
		Preload("Document").
		Preload("CreatedBy").
		Where("source_type = ?", "manual")

	// Apply date filters for manual tasks
	if fromTime != nil && toTime != nil {
		// Extend toTime to end of day to ensure we capture events on the end date
		endOfDay := time.Date(toTime.Year(), toTime.Month(), toTime.Day(), 23, 59, 59, 999999999, toTime.Location())
		taskQuery = taskQuery.Where("(start_date BETWEEN ? AND ?) OR (end_date BETWEEN ? AND ?) OR (start_date <= ? AND end_date >= ?)",
			fromTime, endOfDay, fromTime, endOfDay, fromTime, endOfDay)
	} else if fromTime != nil {
		taskQuery = taskQuery.Where("start_date >= ? OR end_date >= ?", fromTime, fromTime)
	} else if toTime != nil {
		endOfDay := time.Date(toTime.Year(), toTime.Month(), toTime.Day(), 23, 59, 59, 999999999, toTime.Location())
		taskQuery = taskQuery.Where("start_date <= ? OR end_date <= ?", endOfDay, endOfDay)
	}

	// Apply agency filter for manual tasks
	if agencyID != "" {
		taskQuery = taskQuery.Where("agency_id = ?", agencyID)
	}

	// Apply event type filter for manual tasks
	eventType := c.Query("event_type")
	if eventType != "" {
		taskQuery = taskQuery.Where("type = ?", eventType)
	}

	taskQuery.Find(&manualTasks)

	// Add manually created calendar events
	for _, task := range manualTasks {
		if task.StartDate != nil {
			eventData := gin.H{
				"id":          fmt.Sprintf("task_%d", task.ID),
				"title":       task.Title,
				"date":        task.StartDate.Format("2006-01-02"),
				"type":        string(task.Type),
				"description": task.Description,
				"task_id":     task.ID,
				"location":    task.Location,
				"url":         task.URL,
				"is_all_day":  task.IsAllDay,
				"is_public":   task.IsPublic,
			}

			// Add time if not all day
			if !task.IsAllDay {
				eventData["time"] = task.StartDate.Format("15:04")
			}

			// Add end date if available
			if task.EndDate != nil {
				eventData["end_date"] = task.EndDate.Format("2006-01-02")
				if !task.IsAllDay {
					eventData["end_time"] = task.EndDate.Format("15:04")
				}
			}

			// Add agency information if available
			if task.Agency != nil && task.Agency.ID != 0 {
				eventData["agency"] = task.Agency.ShortName
				eventData["agency_id"] = task.Agency.ID
			}

			// Add document information if available
			if task.Document != nil && task.Document.ID != 0 {
				eventData["document_id"] = task.Document.ID
				eventData["document_title"] = task.Document.Title
			}

			// Add regulation information if available
			if task.RegulationID != nil {
				eventData["regulation_id"] = *task.RegulationID
			}

			events = append(events, eventData)
		}
	}

	// Build response
	calendar := gin.H{
		"documents": gin.H{
			"not_yet_effective": notYetEffective,
			"effective":         effective,
			"terminated":        terminated,
		},
		"events": events,
		"view":   view,
		"date_range": gin.H{
			"start": fromDate,
			"end":   toDate,
		},
		"filters": gin.H{
			"agency_id": agencyID,
			"doc_type":  docType,
		},
	}

	c.JSON(http.StatusOK, calendar)
}

// GetCalendarStats returns calendar statistics
func GetCalendarStats(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Count documents that are not yet effective
	var notYetEffectiveCount int64
	db.Model(&models.Document{}).
		Where("is_public = ? AND visibility_level = ? AND effective_date IS NOT NULL AND effective_date > NOW()", true, 1).
		Count(&notYetEffectiveCount)

	// Count documents that are currently effective
	var effectiveCount int64
	db.Model(&models.Document{}).
		Where("is_public = ? AND visibility_level = ? AND effective_date IS NOT NULL AND effective_date <= NOW() AND (termination_date IS NULL OR termination_date > NOW())", true, 1).
		Count(&effectiveCount)

	// Count documents that are terminated
	var terminatedCount int64
	db.Model(&models.Document{}).
		Where("is_public = ? AND visibility_level = ? AND termination_date IS NOT NULL AND termination_date <= NOW()", true, 1).
		Count(&terminatedCount)

	// Count upcoming public hearings (next 30 days)
	thirtyDaysFromNow := time.Now().AddDate(0, 0, 30)
	var upcomingHearingsCount int64
	db.Model(&models.Document{}).
		Where("is_public = ? AND visibility_level = ? AND public_hearing_date IS NOT NULL AND public_hearing_date BETWEEN NOW() AND ?", true, 1, thirtyDaysFromNow).
		Count(&upcomingHearingsCount)

	// Count upcoming comment deadlines (next 30 days)
	var upcomingDeadlinesCount int64
	db.Model(&models.Document{}).
		Where("is_public = ? AND visibility_level = ? AND comment_due_date IS NOT NULL AND comment_due_date BETWEEN NOW() AND ?", true, 1, thirtyDaysFromNow).
		Count(&upcomingDeadlinesCount)

	// Count total active agencies
	var totalAgencies int64
	db.Model(&models.Agency{}).
		Where("is_active = ?", true).
		Count(&totalAgencies)

	// Count recent documents (last 30 days)
	var recentDocuments int64
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	db.Model(&models.Document{}).
		Where("is_public = ? AND visibility_level = ? AND created_at >= ?", true, 1, thirtyDaysAgo).
		Count(&recentDocuments)

	stats := gin.H{
		"document_effectiveness": gin.H{
			"not_yet_effective": notYetEffectiveCount,
			"effective":         effectiveCount,
			"terminated":        terminatedCount,
		},
		"upcoming_events": gin.H{
			"hearings":  upcomingHearingsCount,
			"deadlines": upcomingDeadlinesCount,
		},
		"totals": gin.H{
			"agencies":         totalAgencies,
			"recent_documents": recentDocuments,
		},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Calendar statistics retrieved successfully",
		Data:    stats,
	})
}

// CalendarEventData represents calendar event data
type CalendarEventData struct {
	ID          uint       `json:"id"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Type        string     `json:"type"`
	StartDate   time.Time  `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
	IsAllDay    bool       `json:"is_all_day"`
	Location    string     `json:"location"`
	URL         string     `json:"url"`
	DocumentID  *uint      `json:"document_id"`
	AgencyID    *uint      `json:"agency_id"`
	IsPublic    bool       `json:"is_public"`
	CreatedAt   string     `json:"created_at"`
	UpdatedAt   string     `json:"updated_at"`
	CreatedBy   gin.H      `json:"created_by,omitempty"`
	Document    gin.H      `json:"document,omitempty"`
	Agency      gin.H      `json:"agency,omitempty"`
}

// CreateCalendarEvent creates a new calendar event
func CreateCalendarEvent(c *gin.Context) {
	var req struct {
		Title       string     `json:"title" binding:"required"`
		Description string     `json:"description"`
		Type        string     `json:"type" binding:"required"`
		StartDate   time.Time  `json:"start_date" binding:"required"`
		EndDate     *time.Time `json:"end_date"`
		IsAllDay    bool       `json:"is_all_day"`
		Location    string     `json:"location"`
		URL         string     `json:"url"`
		DocumentID  *uint      `json:"document_id"`
		AgencyID    *uint      `json:"agency_id"`
		IsPublic    bool       `json:"is_public"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Validate document if provided
	if req.DocumentID != nil {
		var document models.Document
		if err := db.First(&document, *req.DocumentID).Error; err != nil {
			HandleBadRequest(c, "Invalid document ID")
			return
		}
	}

	// Validate agency if provided
	if req.AgencyID != nil {
		var agency models.Agency
		if err := db.First(&agency, *req.AgencyID).Error; err != nil {
			HandleBadRequest(c, "Invalid agency ID")
			return
		}
	}
	validTaskTypes := map[string]bool{
		"review":    true,
		"deadline":  true,
		"hearing":   true,
		"comment":   true,
		"general":   true,
		"reminder":  true,
		"follow_up": true,
	}

	// Validate task type
	if !validTaskTypes[req.Type] {
		HandleBadRequest(c, "Invalid task type: "+req.Type)
		return
	}
	// Create calendar event (using Task model for calendar events)
	// Set the type from request, default to "general" if not provided
	eventType := "general"
	if req.Type != "" {
		eventType = req.Type
	}

	event := models.Task{
		Title:       req.Title,
		Description: req.Description,
		Type:        models.TaskType(eventType),
		StartDate:   &req.StartDate,
		EndDate:     req.EndDate,
		IsAllDay:    req.IsAllDay,
		Location:    req.Location,
		URL:         req.URL,
		DocumentID:  req.DocumentID,
		AgencyID:    req.AgencyID,
		IsPublic:    req.IsPublic,
		SourceType:  "manual",
		Status:      models.TaskStatus("pending"),  // Explicitly cast to TaskStatus
		Priority:    models.TaskPriority("medium"), // Explicitly cast to TaskPriority
		CreatedByID: userID.(uint),
	}

	if err := db.Create(&event).Error; err != nil {
		// Debug: log the event details
		fmt.Printf("DEBUG: Calendar event creation failed. Event details: Status=%s, Priority=%s, Type=%s, Error=%s\n",
			event.Status, event.Priority, event.Type, err.Error())
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create calendar event",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Calendar event created successfully",
		Data:    gin.H{"id": event.ID},
	})
}

// GetCalendarEvent returns a specific calendar event
func GetCalendarEvent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get calendar event with related data
	var event models.Task
	if err := db.Preload("CreatedBy").
		Preload("Document").
		Preload("Agency").
		Where("source_type = ?", "manual").
		First(&event, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Calendar event")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch calendar event",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	eventData := CalendarEventData{
		ID:          event.ID,
		Title:       event.Title,
		Description: event.Description,
		Type:        string(event.Type),
		StartDate:   *event.StartDate,
		EndDate:     event.EndDate,
		IsAllDay:    event.IsAllDay,
		Location:    event.Location,
		URL:         event.URL,
		DocumentID:  event.DocumentID,
		AgencyID:    event.AgencyID,
		IsPublic:    event.IsPublic,
		CreatedAt:   event.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   event.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Add created by information
	if event.CreatedBy.ID != 0 {
		eventData.CreatedBy = gin.H{
			"id":         event.CreatedBy.ID,
			"username":   event.CreatedBy.Username,
			"first_name": event.CreatedBy.FirstName,
			"last_name":  event.CreatedBy.LastName,
		}
	}

	// Add document information
	if event.Document != nil && event.Document.ID != 0 {
		eventData.Document = gin.H{
			"id":    event.Document.ID,
			"title": event.Document.Title,
		}
	}

	// Add agency information
	if event.Agency != nil && event.Agency.ID != 0 {
		eventData.Agency = gin.H{
			"id":   event.Agency.ID,
			"name": event.Agency.Name,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Calendar event retrieved successfully",
		Data:    eventData,
	})
}

// UpdateCalendarEvent updates a calendar event
func UpdateCalendarEvent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Title       string     `json:"title"`
		Description string     `json:"description"`
		Type        string     `json:"type"`
		StartDate   *time.Time `json:"start_date"`
		EndDate     *time.Time `json:"end_date"`
		IsAllDay    *bool      `json:"is_all_day"`
		Location    string     `json:"location"`
		URL         string     `json:"url"`
		DocumentID  *uint      `json:"document_id"`
		AgencyID    *uint      `json:"agency_id"`
		IsPublic    *bool      `json:"is_public"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get existing calendar event
	var event models.Task
	if err := db.Where("source_type = ?", "manual").First(&event, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Calendar event")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch calendar event",
			Message: err.Error(),
		})
		return
	}

	// Validate document if provided
	if req.DocumentID != nil {
		var document models.Document
		if err := db.First(&document, *req.DocumentID).Error; err != nil {
			HandleBadRequest(c, "Invalid document ID")
			return
		}
	}

	// Validate agency if provided
	if req.AgencyID != nil {
		var agency models.Agency
		if err := db.First(&agency, *req.AgencyID).Error; err != nil {
			HandleBadRequest(c, "Invalid agency ID")
			return
		}
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Type != "" {
		updates["type"] = models.TaskType(req.Type)
	}
	if req.StartDate != nil {
		updates["start_date"] = req.StartDate
	}
	if req.EndDate != nil {
		updates["end_date"] = req.EndDate
	}
	if req.IsAllDay != nil {
		updates["is_all_day"] = *req.IsAllDay
	}
	if req.Location != "" {
		updates["location"] = req.Location
	}
	if req.URL != "" {
		updates["url"] = req.URL
	}
	if req.DocumentID != nil {
		if *req.DocumentID == 0 {
			updates["document_id"] = nil
		} else {
			updates["document_id"] = *req.DocumentID
		}
	}
	if req.AgencyID != nil {
		if *req.AgencyID == 0 {
			updates["agency_id"] = nil
		} else {
			updates["agency_id"] = *req.AgencyID
		}
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}

	if len(updates) > 0 {
		if err := db.Model(&event).Updates(updates).Error; err != nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "Failed to update calendar event",
				Message: err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Calendar event updated successfully",
		Data:    gin.H{"id": id},
	})
}

// DeleteCalendarEvent deletes a calendar event
func DeleteCalendarEvent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Check if calendar event exists
	var event models.Task
	if err := db.Where("source_type = ?", "manual").First(&event, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Calendar event")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch calendar event",
			Message: err.Error(),
		})
		return
	}

	// Delete the calendar event
	if err := db.Delete(&event).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete calendar event",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Calendar event deleted successfully",
	})
}

// GetDocumentEffectiveness returns effectiveness status for a specific document
func GetDocumentEffectiveness(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get document with agency information
	var document models.Document
	if err := db.Preload("Agency").First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Determine effectiveness status
	now := time.Now()
	var status string
	var daysUntilChange int
	var changeDate *time.Time

	if document.EffectiveDate == nil {
		status = "no_effective_date"
	} else if document.EffectiveDate.After(now) {
		status = "not_yet_effective"
		daysUntilChange = int(document.EffectiveDate.Sub(now).Hours() / 24)
		changeDate = document.EffectiveDate
	} else if document.TerminationDate != nil && document.TerminationDate.Before(now) {
		status = "terminated"
		daysUntilChange = int(now.Sub(*document.TerminationDate).Hours() / 24)
		changeDate = document.TerminationDate
	} else {
		status = "effective"
		if document.TerminationDate != nil {
			daysUntilChange = int(document.TerminationDate.Sub(now).Hours() / 24)
			changeDate = document.TerminationDate
		}
	}

	effectiveness := gin.H{
		"document_id":       document.ID,
		"title":             document.Title,
		"status":            status,
		"publication_date":  document.PublicationDate,
		"effective_date":    document.EffectiveDate,
		"termination_date":  document.TerminationDate,
		"days_until_change": daysUntilChange,
		"change_date":       changeDate,
		"agency": gin.H{
			"id":         document.Agency.ID,
			"name":       document.Agency.Name,
			"short_name": document.Agency.ShortName,
		},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document effectiveness retrieved successfully",
		Data:    effectiveness,
	})
}
