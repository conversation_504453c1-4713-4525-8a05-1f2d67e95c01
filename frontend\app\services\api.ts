import axios, { AxiosInstance } from 'axios';
import { logAxiosError } from '../utils/errorLogger';
import {
  ApiResponse,
  PaginatedResponse,
  User,
  Document,
  Agency,
  Category,
  Tag,
  Subject,
  SearchParams,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  DocumentFormData,
  CommentFormData,
  SignedCommentFormData,
  DocumentComment,
  DigitalCertificate,
  DigitalSignature,
  DocumentFile,
  Summary,
  SummaryFilters,
  Task,
  TaskFormData,
  TaskFilters,
  ParseTextRequest,
  ProcessingResult,
  DigitalCertificateExtended,
  DigitalSignatureExtended,
  RetentionPolicy,
  ProcessingJob
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }> = [];

  constructor() {
    this.baseURL = (process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8080') + '/api/v1';

    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000'),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken();

        if (token && token !== 'null' && token !== 'undefined' && token.trim() !== '') {
          config.headers.Authorization = `Bearer ${token}`;
        } else {
          if (token === 'null' || token === 'undefined') {
            console.error('Prevented sending invalid token string:', token);
            this.clearTokens();
          }
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        // Log all axios errors
        logAxiosError(error, error.config);

        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          if (this.isRefreshing) {
            // If already refreshing, queue this request
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.api(originalRequest);
            }).catch(err => {
              return Promise.reject(err);
            });
          }

          this.isRefreshing = true;

          try {
            const refreshToken = this.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              this.setToken(response.token);
              if (response.refresh_token) {
                this.setRefreshToken(response.refresh_token);
              }

              // Process the failed queue
              this.processQueue(null, response.token);

              originalRequest.headers.Authorization = `Bearer ${response.token}`;
              return this.api(originalRequest);
            } else {
              // No refresh token available, clear tokens and redirect
              this.processQueue(error, null);
              this.clearTokens();
              window.location.href = '/login';
              return Promise.reject(error);
            }
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            this.clearTokens();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }



  // Token management
  private validateToken(token: string | null): boolean {
    if (!token) return false;

    // JWT should have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid token format: expected 3 parts, got', parts.length);
      console.error('Token:', token);
      return false;
    }

    // Each part should be non-empty
    if (parts.some(part => part.length === 0)) {
      console.error('Invalid token format: empty parts detected');
      console.error('Token:', token);
      return false;
    }

    return true;
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      const tokenKey = process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token';
      const token = localStorage.getItem(tokenKey);

      // Handle the case where localStorage contains the string "null"
      if (token === 'null' || token === 'undefined') {
        console.warn('Found invalid token string in localStorage, removing');
        localStorage.removeItem(tokenKey);
        // Also clear the cookie
        document.cookie = `${tokenKey}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
        return null;
      }

      if (token && !this.validateToken(token)) {
        console.error('Removing invalid token from localStorage');
        localStorage.removeItem(tokenKey);
        // Also clear the cookie
        document.cookie = `${tokenKey}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
        return null;
      }
      return token;
    }
    return null;
  }

  private getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem(process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token');

      // Handle the case where localStorage contains the string "null"
      if (token === 'null' || token === 'undefined') {
        console.warn('Found invalid refresh token string in localStorage, removing');
        localStorage.removeItem(process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token');
        return null;
      }

      return token;
    }
    return null;
  }

  private setToken(token: string | null): void {
    if (token === null || token === undefined) {
      console.warn('Attempting to set null/undefined token, clearing instead');
      this.clearTokens();
      return;
    }

    if (!this.validateToken(token)) {
      console.error('Attempting to set invalid token, rejecting');
      return;
    }

    if (typeof window !== 'undefined') {
      const tokenKey = process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token';
      localStorage.setItem(tokenKey, token);
      // Also set as cookie for middleware
      document.cookie = `${tokenKey}=${token}; path=/; max-age=${24 * 60 * 60}; SameSite=Lax`;
      console.log('Token set successfully, segments:', token.split('.').length);
    }
  }

  private setRefreshToken(token: string | null): void {
    if (token === null || token === undefined) {
      console.warn('Attempting to set null/undefined refresh token, clearing instead');
      if (typeof window !== 'undefined') {
        const refreshKey = process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token';
        localStorage.removeItem(refreshKey);
        document.cookie = `${refreshKey}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
      }
      return;
    }

    if (typeof window !== 'undefined') {
      const refreshKey = process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token';
      localStorage.setItem(refreshKey, token);
      // Also set as cookie for middleware
      document.cookie = `${refreshKey}=${token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
    }
  }

  private clearTokens(): void {
    if (typeof window !== 'undefined') {
      const tokenKey = process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token';
      const refreshKey = process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token';

      localStorage.removeItem(tokenKey);
      localStorage.removeItem(refreshKey);

      // Clear cookies
      document.cookie = `${tokenKey}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
      document.cookie = `${refreshKey}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    }
  }

  // Public method to clear all auth data
  clearAllAuthData(): void {
    this.clearTokens();
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/login', credentials);
    this.setToken(response.data.token);
    this.setRefreshToken(response.data.refresh_token);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/register', userData);
    this.setToken(response.data.token);
    this.setRefreshToken(response.data.refresh_token);
    return response.data;
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/refresh', {
      refresh_token: refreshToken,
    });
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } finally {
      this.clearTokens();
    }
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    const response = await this.api.get<ApiResponse<User>>('/auth/me');
    return response.data;
  }

  async updateProfile(profileData: Partial<User>): Promise<ApiResponse<User>> {
    const response = await this.api.put<ApiResponse<User>>('/auth/profile', profileData);
    return response.data;
  }

  async getUserPermissions(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/auth/permissions');
    return response.data;
  }

  async getUserStats(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/auth/stats');
    return response.data;
  }

  async getDashboardStats(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/user/dashboard-stats');
    return response.data;
  }

  async changePassword(passwordData: { current_password: string; new_password: string; confirm_password: string }): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/auth/change-password', passwordData);
    return response.data;
  }

  async forgotPassword(data: { email: string }): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/auth/forgot-password', data);
    return response.data;
  }

  async verifyEmail(token: string, email: string): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/auth/verify-email', { token, email });
    return response.data;
  }

  async resendVerificationEmail(email: string): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/auth/resend-verification', { email });
    return response.data;
  }

  async resetPassword(data: { token: string; password: string; confirm_password: string }): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/auth/reset-password', data);
    return response.data;
  }

  async validateResetToken(token: string, email: string): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/auth/validate-reset-token', { token, email });
    return response.data;
  }

  // User Management API
  async getUsers(params?: { page?: number; per_page?: number }): Promise<ApiResponse<User[]>> {
    const response = await this.api.get<ApiResponse<User[]>>('/users', { params });
    return response.data;
  }

  async getUser(id: number): Promise<ApiResponse<User>> {
    const response = await this.api.get<ApiResponse<User>>(`/users/${id}`);
    return response.data;
  }

  async createUser(userData: any): Promise<ApiResponse<User>> {
    const response = await this.api.post<ApiResponse<User>>('/users', userData);
    return response.data;
  }

  async updateUser(id: number, userData: any): Promise<ApiResponse<User>> {
    const response = await this.api.put<ApiResponse<User>>(`/users/${id}`, userData);
    return response.data;
  }

  async deleteUser(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/users/${id}`);
    return response.data;
  }

  // User Preferences API
  async getUserPreferences(userId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/users/${userId}/preferences`);
    return response.data;
  }

  async updateUserPreferences(userId: number, preferences: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/users/${userId}/preferences`, preferences);
    return response.data;
  }

  // Calendar API
  async getCalendar(params?: { from?: string; to?: string; agency_id?: string; type?: string }): Promise<any> {
    const response = await this.api.get<any>('/public/calendar', { params });
    return response.data;
  }

  async getCalendarStats(): Promise<any> {
    const response = await this.api.get<any>('/public/calendar/stats');
    return response.data;
  }

  // Calendar Events API
  async createCalendarEvent(data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/calendar/events', data);
    return response.data;
  }

  async getCalendarEvent(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/calendar/events/${id}`);
    return response.data;
  }

  async getCalendarEvents(): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>('/calendar/events');
    return response.data;
  }

  async updateCalendarEvent(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/calendar/events/${id}`, data);
    return response.data;
  }

  async deleteCalendarEvent(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/calendar/events/${id}`);
    return response.data;
  }

  // Document endpoints
  async getPublicDocuments(params?: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>('/public/documents', { params });
    return response.data;
  }



  async getPublicDocument(id: number): Promise<Document> {
    const response = await this.api.get<Document>(`/public/documents/${id}`);
    return response.data;
  }



  async trackDocumentView(id: number): Promise<ApiResponse<{ views: number }>> {
    const response = await this.api.post<ApiResponse<{ views: number }>>(`/documents/${id}/view`);
    return response.data;
  }

  async submitDocumentComment(id: number, commentData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/documents/${id}/comments`, commentData);
    return response.data;
  }

  async updateDocumentStatus(id: number, statusData: { status: string; comment?: string }): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/documents/${id}/status`, statusData);
    return response.data;
  }

  async assignDocument(id: number, assignmentData: { assignee_id: string }): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/documents/${id}/assign`, assignmentData);
    return response.data;
  }

  async searchPublicDocuments(params: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>('/public/documents/search', { params });
    return response.data;
  }

  async getDocuments(params?: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>('/documents', { params });
    return response.data;
  }

  async getDocument(id: number): Promise<Document> {
    const response = await this.api.get<Document>(`/documents/${id}`);
    return response.data;
  }

  async createDocument(data: DocumentFormData): Promise<ApiResponse<{ id: number }>> {
    const response = await this.api.post<ApiResponse<{ id: number }>>('/documents', data);
    return response.data;
  }

  async updateDocument(id: number, data: Partial<DocumentFormData>): Promise<ApiResponse<{ id: number }>> {
    const response = await this.api.put<ApiResponse<{ id: number }>>(`/documents/${id}`, data);
    return response.data;
  }

  async deleteDocument(id: number): Promise<ApiResponse<void>> {
    const response = await this.api.delete<ApiResponse<void>>(`/documents/${id}`);
    return response.data;
  }

  async searchDocuments(params: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>('/documents/search', { params });
    return response.data;
  }

  // Document workflow
  async submitDocument(id: number): Promise<ApiResponse<{ id: number; status: string }>> {
    const response = await this.api.post<ApiResponse<{ id: number; status: string }>>(`/documents/${id}/submit`);
    return response.data;
  }

  async approveDocument(id: number): Promise<ApiResponse<{ id: number; status: string }>> {
    const response = await this.api.post<ApiResponse<{ id: number; status: string }>>(`/documents/${id}/approve`);
    return response.data;
  }

  async publishDocument(id: number): Promise<ApiResponse<{ id: number; status: string }>> {
    const response = await this.api.post<ApiResponse<{ id: number; status: string }>>(`/documents/${id}/publish`);
    return response.data;
  }

  async withdrawDocument(id: number): Promise<ApiResponse<{ id: number; status: string }>> {
    const response = await this.api.post<ApiResponse<{ id: number; status: string }>>(`/documents/${id}/withdraw`);
    return response.data;
  }

  // Document comments
  async createPublicComment(documentId: number, data: CommentFormData): Promise<ApiResponse<{ document_id: number; comment_id: number }>> {
    const response = await this.api.post<ApiResponse<{ document_id: number; comment_id: number }>>(`/public/documents/${documentId}/comments`, data);
    return response.data;
  }

  async getDocumentComments(documentId: number, params?: { page?: number; per_page?: number; verified?: boolean; public?: boolean }): Promise<PaginatedResponse<DocumentComment>> {
    const response = await this.api.get<PaginatedResponse<DocumentComment>>(`/documents/${documentId}/comments`, { params });
    return response.data;
  }

  // Digital Signature Comment Methods
  async createSignedComment(documentId: number, data: SignedCommentFormData): Promise<ApiResponse<{ document_id: number; comment_id: number; signature_id: string; verified: boolean }>> {
    const response = await this.api.post<ApiResponse<{ document_id: number; comment_id: number; signature_id: string; verified: boolean }>>(`/documents/${documentId}/comments/signed`, data);
    return response.data;
  }

  async verifyCommentSignature(commentId: number): Promise<ApiResponse<DigitalSignature>> {
    const response = await this.api.get<ApiResponse<DigitalSignature>>(`/comments/${commentId}/verify`);
    return response.data;
  }

  // Digital Certificate Management
  async getUserCertificates(): Promise<ApiResponse<DigitalCertificate[]>> {
    const response = await this.api.get<ApiResponse<DigitalCertificate[]>>('/user/certificates');
    return response.data;
  }

  async getCertificate(certificateId: number): Promise<ApiResponse<DigitalCertificate>> {
    const response = await this.api.get<ApiResponse<DigitalCertificate>>(`/certificates/${certificateId}`);
    return response.data;
  }

  // Agency endpoints
  async getPublicAgencies(params?: { page?: number; per_page?: number }): Promise<PaginatedResponse<Agency>> {
    const response = await this.api.get<PaginatedResponse<Agency>>('/public/agencies', { params });
    return response.data;
  }

  async getPublicAgency(id: number): Promise<{ data: Agency }> {
    const response = await this.api.get<Agency>(`/public/agencies/${id}`);
    return { data: response.data };
  }

  async getAgencyDocuments(agencyId: number, params?: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>(`/public/agencies/${agencyId}/documents`, { params });
    return response.data;
  }

  async getAgencies(params?: { page?: number; per_page?: number }): Promise<PaginatedResponse<Agency>> {
    const response = await this.api.get<PaginatedResponse<Agency>>('/agencies', { params });
    return response.data;
  }

  async getAgency(id: number): Promise<{ data: Agency }> {
    const response = await this.api.get<Agency>(`/agencies/${id}`);
    return { data: response.data };
  }

  async createAgency(agencyData: any): Promise<ApiResponse<Agency>> {
    const response = await this.api.post<ApiResponse<Agency>>('/agencies', agencyData);
    return response.data;
  }

  async updateAgency(id: number, agencyData: any): Promise<ApiResponse<Agency>> {
    const response = await this.api.put<ApiResponse<Agency>>(`/agencies/${id}`, agencyData);
    return response.data;
  }

  async deleteAgency(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/agencies/${id}`);
    return response.data;
  }

  // Agency contacts management
  async getAgencyContacts(agencyId: number): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>(`/agencies/${agencyId}/contacts`);
    return response.data;
  }

  async createAgencyContact(agencyId: number, contact: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/agencies/${agencyId}/contacts`, contact);
    return response.data;
  }

  async getAgencyContact(contactId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/agencies/contacts/${contactId}`);
    return response.data;
  }

  async updateAgencyContact(contactId: number, contact: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/agencies/contacts/${contactId}`, contact);
    return response.data;
  }

  async deleteAgencyContact(contactId: number): Promise<void> {
    await this.api.delete(`/agencies/contacts/${contactId}`);
  }

  // Category endpoints
  async getPublicCategories(params?: { page?: number; per_page?: number }): Promise<{ data: Category[] }> {
    const response = await this.api.get<{ message: string; data: Category[] }>('/public/categories', { params });
    return { data: response.data.data };
  }

  async getPublicCategory(id: number): Promise<{ data: Category }> {
    const response = await this.api.get<Category>(`/public/categories/${id}`);
    return { data: response.data };
  }

  async getCategoryDocuments(categoryId: number, params?: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>(`/public/categories/${categoryId}/documents`, { params });
    return response.data;
  }

  async getCategories(params?: { page?: number; per_page?: number }): Promise<ApiResponse<Category[]>> {
    const response = await this.api.get<ApiResponse<Category[]>>('/categories', { params });
    return response.data;
  }

  async getCategory(id: number): Promise<{ data: Category }> {
    const response = await this.api.get<Category>(`/categories/${id}`);
    return { data: response.data };
  }

  async createCategory(categoryData: any): Promise<ApiResponse<Category>> {
    const response = await this.api.post<ApiResponse<Category>>('/categories', categoryData);
    return response.data;
  }

  async updateCategory(id: number, categoryData: any): Promise<ApiResponse<Category>> {
    const response = await this.api.put<ApiResponse<Category>>(`/categories/${id}`, categoryData);
    return response.data;
  }

  async deleteCategory(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/categories/${id}`);
    return response.data;
  }

  // Tag endpoints
  async getPublicTags(): Promise<ApiResponse<Tag[]>> {
    const response = await this.api.get<ApiResponse<Tag[]>>('/public/tags');
    return response.data;
  }

  async getTagDocuments(tagId: number, params?: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>(`/public/tags/${tagId}/documents`, { params });
    return response.data;
  }

  // Tag Management API (requires authentication)
  async getTags(params?: any): Promise<PaginatedResponse<Tag>> {
    const response = await this.api.get<PaginatedResponse<Tag>>('/tags', { params });
    return response.data;
  }

  async getTag(id: number): Promise<ApiResponse<Tag>> {
    const response = await this.api.get<ApiResponse<Tag>>(`/tags/${id}`);
    return response.data;
  }

  async createTag(tagData: any): Promise<ApiResponse<Tag>> {
    const response = await this.api.post<ApiResponse<Tag>>('/tags', tagData);
    return response.data;
  }

  async updateTag(id: number, tagData: any): Promise<ApiResponse<Tag>> {
    const response = await this.api.put<ApiResponse<Tag>>(`/tags/${id}`, tagData);
    return response.data;
  }

  async deleteTag(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/tags/${id}`);
    return response.data;
  }

  // Subject Management API (requires authentication)
  async getSubjects(params?: any): Promise<PaginatedResponse<Subject>> {
    const response = await this.api.get<PaginatedResponse<Subject>>('/subjects', { params });
    return response.data;
  }

  async getSubject(id: number): Promise<ApiResponse<Subject>> {
    const response = await this.api.get<ApiResponse<Subject>>(`/subjects/${id}`);
    return response.data;
  }

  async createSubject(subjectData: any): Promise<ApiResponse<Subject>> {
    const response = await this.api.post<ApiResponse<Subject>>('/subjects', subjectData);
    return response.data;
  }

  async updateSubject(id: number, subjectData: any): Promise<ApiResponse<Subject>> {
    const response = await this.api.put<ApiResponse<Subject>>(`/subjects/${id}`, subjectData);
    return response.data;
  }

  async deleteSubject(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/subjects/${id}`);
    return response.data;
  }

  // Subject endpoints
  async getPublicSubjects(): Promise<ApiResponse<Subject[]>> {
    const response = await this.api.get<ApiResponse<Subject[]>>('/public/subjects');
    return response.data;
  }

  async getSubjectDocuments(subjectId: number, params?: SearchParams): Promise<PaginatedResponse<Document>> {
    const response = await this.api.get<PaginatedResponse<Document>>(`/public/subjects/${subjectId}/documents`, { params });
    return response.data;
  }

  // File endpoints - Public access
  async getPublicDocumentFiles(documentId: number): Promise<ApiResponse<DocumentFile[]>> {
    const response = await this.api.get<ApiResponse<DocumentFile[]>>(`/public/documents/${documentId}/files`);
    return response.data;
  }

  async downloadPublicFile(fileId: number): Promise<Blob> {
    const response = await this.api.get(`/public/files/${fileId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // Generic HTTP methods
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.api.get<T>(url, { params });
    return response.data;
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.post<T>(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<T>(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<T>(url);
    return response.data;
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  getAuthToken(): string | null {
    return this.getToken();
  }

  // Summary API methods
  async getPublicSummaries(filters?: SummaryFilters): Promise<PaginatedResponse<Summary>> {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await this.api.get<PaginatedResponse<Summary>>(`/public/summaries?${params.toString()}`);
    return response.data;
  }

  async getSummaries(filters?: SummaryFilters): Promise<PaginatedResponse<Summary>> {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await this.api.get<PaginatedResponse<Summary>>(`/summaries?${params.toString()}`);
    return response.data;
  }

  async getSummary(id: number): Promise<Summary> {
    const response = await this.api.get<Summary>(`/summaries/${id}`);
    return response.data;
  }

  // Task endpoints
  async getTasks(filters?: TaskFilters): Promise<PaginatedResponse<Task>> {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await this.api.get<PaginatedResponse<Task>>(`/tasks?${params.toString()}`);
    return response.data;
  }

  async getTask(id: number): Promise<Task> {
    const response = await this.api.get<Task>(`/tasks/${id}`);
    return response.data;
  }

  async createTask(taskData: TaskFormData): Promise<ApiResponse<{ id: number; title: string }>> {
    const response = await this.api.post<ApiResponse<{ id: number; title: string }>>('/tasks', taskData);
    return response.data;
  }

  async updateTask(id: number, taskData: Partial<TaskFormData>): Promise<ApiResponse<{ id: number }>> {
    const response = await this.api.put<ApiResponse<{ id: number }>>(`/tasks/${id}`, taskData);
    return response.data;
  }

  async deleteTask(id: number): Promise<ApiResponse<{ id: number }>> {
    const response = await this.api.delete<ApiResponse<{ id: number }>>(`/tasks/${id}`);
    return response.data;
  }

  async completeTask(id: number): Promise<ApiResponse<{ id: number }>> {
    const response = await this.api.post<ApiResponse<{ id: number }>>(`/tasks/${id}/complete`);
    return response.data;
  }

  // Intelligent text parsing
  async parseText(request: ParseTextRequest): Promise<ApiResponse<ProcessingResult>> {
    const response = await this.api.post<ApiResponse<ProcessingResult>>('/tasks/parse-text', request);
    return response.data;
  }

  // Document Reviews API
  async createDocumentReview(documentId: number, reviewData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/documents/${documentId}/reviews`, reviewData);
    return response.data;
  }

  async getDocumentReviews(documentId: number): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>(`/documents/${documentId}/reviews`);
    return response.data;
  }

  async getDocumentReview(reviewId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/reviews/${reviewId}`);
    return response.data;
  }

  async updateDocumentReview(reviewId: number, reviewData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/reviews/${reviewId}`, reviewData);
    return response.data;
  }

  async deleteDocumentReview(reviewId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/reviews/${reviewId}`);
    return response.data;
  }

  // Document Versions API
  async getDocumentVersions(documentId: number): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>(`/documents/${documentId}/versions`);
    return response.data;
  }

  async createDocumentVersion(documentId: number, versionData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/documents/${documentId}/versions`, versionData);
    return response.data;
  }

  async getDocumentVersion(versionId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/versions/${versionId}`);
    return response.data;
  }

  async updateDocumentVersion(versionId: number, versionData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/versions/${versionId}`, versionData);
    return response.data;
  }

  async deleteDocumentVersion(versionId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/versions/${versionId}`);
    return response.data;
  }

  // Legacy Document Files API (kept for compatibility)
  async uploadDocumentFileFormData(documentId: number, fileData: FormData): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/documents/${documentId}/files`, fileData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  }

  async updateDocumentFile(fileId: number, fileData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/documents/files/${fileId}`, fileData);
    return response.data;
  }

  // Legacy admin role methods (kept for compatibility)
  async getAdminRole(roleId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/admin/roles/${roleId}`);
    return response.data;
  }

  async updateAdminRole(roleId: number, roleData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/admin/roles/${roleId}`, roleData);
    return response.data;
  }

  async deleteAdminRole(roleId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/admin/roles/${roleId}`);
    return response.data;
  }

  // Permission Management API
  async getAdminPermissions(): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>('/admin/permissions');
    return response.data;
  }

  // User Role Management API
  async getAdminUserRoles(): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>('/admin/user-roles');
    return response.data;
  }

  async assignUserRoles(userId: number, roleData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/admin/users/${userId}/roles`, roleData);
    return response.data;
  }

  async removeAdminUserRole(userId: number, roleId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/admin/users/${userId}/roles/${roleId}`);
    return response.data;
  }

  async getUserPermissionsAdmin(userId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/admin/users/${userId}/permissions`);
    return response.data;
  }

  // Analytics API
  async getDocumentStats(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/documents/stats');
    return response.data;
  }

  async getAgencyStats(agencyId?: number): Promise<ApiResponse<any>> {
    const url = agencyId ? `/analytics/agencies/${agencyId}/stats` : '/analytics/agencies/stats';
    const response = await this.api.get<ApiResponse<any>>(url);
    return response.data;
  }

  async getRelationshipStats(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/relationships/stats');
    return response.data;
  }

  // Comment Management API
  async getComments(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.api.get<PaginatedResponse<any>>('/comments', { params });
    return response.data;
  }

  async getComment(commentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/comments/${commentId}`);
    return response.data;
  }

  async updateComment(commentId: number, commentData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/comments/${commentId}`, commentData);
    return response.data;
  }

  async deleteComment(commentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/comments/${commentId}`);
    return response.data;
  }

  // Comment Moderation
  async moderateComment(commentId: number, action: 'approve' | 'reject' | 'flag', reason?: string): Promise<ApiResponse<{ comment_id: number; action: string; reason?: string }>> {
    const response = await this.api.post<ApiResponse<{ comment_id: number; action: string; reason?: string }>>(`/comments/${commentId}/moderate`, { action, reason });
    return response.data;
  }

  // Summary Management API
  async createSummary(summaryData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/summaries', summaryData);
    return response.data;
  }

  async updateSummary(summaryId: number, summaryData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/summaries/${summaryId}`, summaryData);
    return response.data;
  }

  async deleteSummary(summaryId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/summaries/${summaryId}`);
    return response.data;
  }

  // Task Comments API
  async getTaskComments(taskId: number): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>(`/tasks/${taskId}/comments`);
    return response.data;
  }

  async createTaskComment(taskId: number, commentData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/tasks/${taskId}/comments`, commentData);
    return response.data;
  }

  async getTaskComment(commentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/task-comments/${commentId}`);
    return response.data;
  }

  async updateTaskComment(commentId: number, commentData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/task-comments/${commentId}`, commentData);
    return response.data;
  }

  async deleteTaskComment(commentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/task-comments/${commentId}`);
    return response.data;
  }

  // Task Performance API methods
  async getTaskPerformance(taskId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/tasks/${taskId}/performance`);
    return response.data;
  }

  async calculateTaskPerformance(taskId: number): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/tasks/${taskId}/performance/calculate`);
    return response.data;
  }

  async updateTaskPerformance(taskId: number, performanceData: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/tasks/${taskId}/performance`, performanceData);
    return response.data;
  }

  async getTaskPerformanceHistory(taskId: number, params?: { page?: number; per_page?: number }): Promise<PaginatedResponse<any>> {
    const response = await this.api.get<PaginatedResponse<any>>(`/tasks/${taskId}/performance/history`, { params });
    return response.data;
  }

  async recalculateAllTaskPerformances(): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/tasks/performance/recalculate-all`);
    return response.data;
  }

  // Regulation API methods
  async getRegulation(id: number): Promise<any> {
    // Use public endpoint for regulation details
    const response = await this.api.get<any>(`/public/regulations/${id}`);
    return response.data;
  }

  async getRegulations(params?: any): Promise<PaginatedResponse<any>> {
    // Check if user is authenticated to determine which endpoint to use
    const token = this.getToken();
    const endpoint = token ? '/regulations' : '/public/regulations';
    const response = await this.api.get<PaginatedResponse<any>>(endpoint, { params });
    return response.data;
  }

  async getRegulationRelationships(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/regulations/${id}/relationships`);
    return response.data;
  }

  async createRegulationRelationship(data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/regulation-relationships', data);
    return response.data;
  }

  async deleteRegulationRelationship(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/regulation-relationships/${id}`);
    return response.data;
  }

  async getRegulationsHierarchy(): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>('/regulations?include_hierarchy=true');
    return response.data;
  }

  async getPublicRegulations(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.api.get<PaginatedResponse<any>>('/public/regulations', { params });
    return response.data;
  }

  // Authenticated regulation methods
  async getAuthenticatedRegulations(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.api.get<PaginatedResponse<any>>('/regulations', { params });
    return response.data;
  }

  async getAuthenticatedRegulation(id: number): Promise<any> {
    const response = await this.api.get<any>(`/regulations/${id}`);
    return response.data;
  }

  async createRegulation(data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/regulations', data);
    return response.data;
  }

  async updateRegulation(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/regulations/${id}`, data);
    return response.data;
  }

  async deleteRegulation(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/regulations/${id}`);
    return response.data;
  }

  // Public stats and analytics
  async getPublicStats(): Promise<any> {
    const response = await this.api.get<any>('/public/stats');
    return response.data;
  }

  // Document workflow methods
  async rejectDocument(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/documents/${id}/reject`, data);
    return response.data;
  }

  // Proceeding management methods
  async getProceedings(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.api.get<PaginatedResponse<any>>('/proceedings', { params });
    return response.data;
  }

  async getProceeding(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/proceedings/${id}`);
    return response.data;
  }

  async createProceeding(data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/proceedings', data);
    return response.data;
  }

  async updateProceeding(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/proceedings/${id}`, data);
    return response.data;
  }

  async deleteProceeding(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/proceedings/${id}`);
    return response.data;
  }

  async getProceedingSteps(id: number): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>(`/proceedings/${id}/steps`);
    return response.data;
  }

  async updateStepStatus(proceedingId: number, stepId: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/proceedings/${proceedingId}/steps/${stepId}/status`, data);
    return response.data;
  }

  async addProceedingLog(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/proceedings/${id}/logs`, data);
    return response.data;
  }

  // Proceeding relationship methods
  async getProceedingRelationships(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/proceedings/${id}/relationships`);
    return response.data;
  }

  async linkProceedingToTask(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/proceedings/${id}/link-task`, data);
    return response.data;
  }

  async linkProceedingToDocument(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/proceedings/${id}/link-document`, data);
    return response.data;
  }

  async linkProceedingToRegulation(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/proceedings/${id}/link-regulation`, data);
    return response.data;
  }

  async triggerProceedingReview(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/proceedings/${id}/trigger-review`);
    return response.data;
  }

  // Individual proceeding step management (if these endpoints exist in backend)
  async getProceedingStep(stepId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/proceeding-steps/${stepId}`);
    return response.data;
  }

  async createProceedingStep(data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/proceeding-steps', data);
    return response.data;
  }

  async updateProceedingStep(stepId: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/proceeding-steps/${stepId}`, data);
    return response.data;
  }

  async deleteProceedingStep(stepId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/proceeding-steps/${stepId}`);
    return response.data;
  }

  async completeProceedingStep(stepId: number): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/proceeding-steps/${stepId}/complete`);
    return response.data;
  }

  // Missing methods for various components
  async submitContactForm(data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/contact', data);
    return response.data;
  }

  async search(params: any): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/search', { params });
    return response.data;
  }

  async getSystemStatus(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/system/status');
    return response.data;
  }

  async getUserSettings(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/user/settings');
    return response.data;
  }

  async updateUserSettings(settings: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>('/user/settings', settings);
    return response.data;
  }

  async updatePreferences(preferences: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>('/user/preferences', preferences);
    return response.data;
  }

  // ============================================================================
  // PRELOADING API METHODS - Missing from frontend
  // ============================================================================

  // Document preloading
  async getDocumentDefaults(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/preloading/documents');
    return response.data;
  }

  async getRegulationDefaults(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/preloading/regulations');
    return response.data;
  }

  async getAgencyDefaults(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/preloading/agencies');
    return response.data;
  }

  async getCategoryDefaults(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/preloading/categories');
    return response.data;
  }

  async getProceedingDefaults(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/preloading/proceedings');
    return response.data;
  }

  async getFinanceDefaults(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/preloading/finances');
    return response.data;
  }

  // Utility preloading endpoints
  async generateSlug(text: string): Promise<ApiResponse<{ slug: string }>> {
    const response = await this.api.get<ApiResponse<{ slug: string }>>(`/preloading/slug?text=${encodeURIComponent(text)}`);
    return response.data;
  }

  async generateFRNumber(): Promise<ApiResponse<{ fr_number: string }>> {
    const response = await this.api.get<ApiResponse<{ fr_number: string }>>('/preloading/fr-number');
    return response.data;
  }

  async generateDocketNumber(agencyId?: number): Promise<ApiResponse<{ docket_number: string }>> {
    const url = agencyId ? `/preloading/docket-number?agency_id=${agencyId}` : '/preloading/docket-number';
    const response = await this.api.get<ApiResponse<{ docket_number: string }>>(url);
    return response.data;
  }

  async getPublicLawNumber(): Promise<ApiResponse<{ public_law_number: string }>> {
    const response = await this.api.get<ApiResponse<{ public_law_number: string }>>('/preloading/public-law-number');
    return response.data;
  }

  async getRegulatoryIdentifier(agencyId?: number): Promise<ApiResponse<{ regulatory_identifier: string }>> {
    const url = agencyId ? `/preloading/regulatory-identifier?agency_id=${agencyId}` : '/preloading/regulatory-identifier';
    const response = await this.api.get<ApiResponse<{ regulatory_identifier: string }>>(url);
    return response.data;
  }

  async getRegulationDocketNumber(agencyId?: number): Promise<ApiResponse<{ regulation_docket_number: string }>> {
    const url = agencyId ? `/preloading/regulation-docket-number?agency_id=${agencyId}` : '/preloading/regulation-docket-number';
    const response = await this.api.get<ApiResponse<{ regulation_docket_number: string }>>(url);
    return response.data;
  }

  // ============================================================================
  // DIGITAL SIGNATURES & CERTIFICATES API - Missing from frontend
  // ============================================================================

  // Digital Signature Management
  async createSignatureRequest(data: { document_id: number; signers: string[]; message?: string }): Promise<ApiResponse<{ signature_id: string }>> {
    const response = await this.api.post<ApiResponse<{ signature_id: string }>>('/signatures/request', data);
    return response.data;
  }

  async signDocument(signatureId: string, data: { signature_data: string; certificate_id?: number }): Promise<ApiResponse<{ signed: boolean; signature_id: string }>> {
    const response = await this.api.post<ApiResponse<{ signed: boolean; signature_id: string }>>(`/signatures/${signatureId}/sign`, data);
    return response.data;
  }

  async rejectSignature(signatureId: string, data: { reason: string }): Promise<ApiResponse<{ rejected: boolean }>> {
    const response = await this.api.post<ApiResponse<{ rejected: boolean }>>(`/signatures/${signatureId}/reject`, data);
    return response.data;
  }

  async validateSignature(signatureId: string): Promise<ApiResponse<{ valid: boolean; details: any }>> {
    const response = await this.api.post<ApiResponse<{ valid: boolean; details: any }>>(`/signatures/${signatureId}/validate`, {});
    return response.data;
  }

  async getUserSignatures(): Promise<ApiResponse<DigitalSignature[]>> {
    const response = await this.api.get<ApiResponse<DigitalSignature[]>>('/signatures/my');
    return response.data;
  }

  // Digital Certificate Management
  async createCertificate(data: { name: string; key_size?: number; validity_days?: number }): Promise<ApiResponse<DigitalCertificate>> {
    const response = await this.api.post<ApiResponse<DigitalCertificate>>('/certificates', data);
    return response.data;
  }

  async getCertificates(params?: any): Promise<PaginatedResponse<DigitalCertificateExtended>> {
    const response = await this.api.get<PaginatedResponse<DigitalCertificateExtended>>('/certificates', { params });
    return response.data;
  }

  async updateCertificate(id: number, data: { name?: string; is_active?: boolean }): Promise<ApiResponse<DigitalCertificate>> {
    const response = await this.api.put<ApiResponse<DigitalCertificate>>(`/certificates/${id}`, data);
    return response.data;
  }

  async revokeCertificate(id: number, data?: { reason?: string }): Promise<ApiResponse<{ revoked: boolean }>> {
    const response = await this.api.post<ApiResponse<{ revoked: boolean }>>(`/certificates/${id}/revoke`, data || {});
    return response.data;
  }

  async deleteCertificate(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/certificates/${id}`);
    return response.data;
  }

  // ============================================================================
  // DOCUMENT PROCESSING & ANALYSIS API - Missing from frontend
  // ============================================================================

  // Document Processing Jobs
  async createProcessingJob(data: { document_id: number; job_type: string; parameters?: any }): Promise<ApiResponse<{ job_id: string }>> {
    const response = await this.api.post<ApiResponse<{ job_id: string }>>('/processing/jobs', data);
    return response.data;
  }

  async getProcessingJobs(params?: { page?: number; per_page?: number; status?: string }): Promise<PaginatedResponse<any>> {
    const response = await this.api.get<PaginatedResponse<any>>('/processing/jobs', { params });
    return response.data;
  }

  async getProcessingJob(jobId: string): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/processing/jobs/${jobId}`);
    return response.data;
  }

  async startProcessingJob(jobId: string): Promise<ApiResponse<{ started: boolean }>> {
    const response = await this.api.post<ApiResponse<{ started: boolean }>>(`/processing/jobs/${jobId}/start`, {});
    return response.data;
  }

  async createProcessingTemplate(data: { name: string; job_type: string; default_parameters: any }): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/processing/templates', data);
    return response.data;
  }

  // Document Analysis
  async getDocumentMetadata(documentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/documents/${documentId}/metadata`);
    return response.data;
  }

  async getDocumentClassification(documentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/documents/${documentId}/classification`);
    return response.data;
  }

  async getDocumentEntities(documentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/documents/${documentId}/entities`);
    return response.data;
  }

  // Document Retention
  async getDocumentRetentionStatus(documentId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/documents/${documentId}/retention-status`);
    return response.data;
  }

  // ============================================================================
  // ROLE MANAGEMENT API - Missing from frontend
  // ============================================================================

  // Role Management
  async getRoles(): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>('/roles');
    return response.data;
  }

  async getRole(roleId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/roles/${roleId}`);
    return response.data;
  }

  async createRole(data: { name: string; description?: string; permissions: string[] }): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/roles', data);
    return response.data;
  }

  async updateRole(roleId: number, data: { name?: string; description?: string; permissions?: string[] }): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/roles/${roleId}`, data);
    return response.data;
  }

  async deleteRole(roleId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/roles/${roleId}`);
    return response.data;
  }

  async assignUserRole(userId: number, roleId: number): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/users/${userId}/roles/${roleId}`, {});
    return response.data;
  }

  async removeUserRole(userId: number, roleId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/users/${userId}/roles/${roleId}`);
    return response.data;
  }

  async getUserRoles(userId: number): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>(`/users/${userId}/roles`);
    return response.data;
  }

  async getPermissions(): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>('/permissions');
    return response.data;
  }

  async getRolePermissions(roleId: number): Promise<ApiResponse<any[]>> {
    const response = await this.api.get<ApiResponse<any[]>>(`/roles/${roleId}/permissions`);
    return response.data;
  }

  // ============================================================================
  // RETENTION POLICIES API - Missing from frontend
  // ============================================================================

  async getRetentionPolicies(params?: any): Promise<PaginatedResponse<RetentionPolicy>> {
    const response = await this.api.get<PaginatedResponse<RetentionPolicy>>('/retention-policies', { params });
    return response.data;
  }

  async getRetentionPolicy(policyId: number): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>(`/retention-policies/${policyId}`);
    return response.data;
  }

  async createRetentionPolicy(data: any): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>('/retention-policies', data);
    return response.data;
  }

  async updateRetentionPolicy(policyId: number, data: any): Promise<ApiResponse<any>> {
    const response = await this.api.put<ApiResponse<any>>(`/retention-policies/${policyId}`, data);
    return response.data;
  }

  async deleteRetentionPolicy(policyId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/retention-policies/${policyId}`);
    return response.data;
  }

  async executeRetentionPolicy(policyId: number): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/retention-policies/${policyId}/execute`);
    return response.data;
  }

  async applyRetentionPolicy(policyId: number, documentIds: number[]): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(`/retention-policies/${policyId}/apply`, { document_ids: documentIds });
    return response.data;
  }

  // ============================================================================
  // ADVANCED ANALYTICS API - Missing from frontend
  // ============================================================================

  async getAdvancedAnalytics(params?: {
    start_date?: string;
    end_date?: string;
    metric_type?: string;
    granularity?: string
  }): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/advanced', { params });
    return response.data;
  }

  async getDocumentAnalytics(params?: any): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/documents', { params });
    return response.data;
  }

  async getUserAnalytics(params?: any): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/users', { params });
    return response.data;
  }

  async getSystemAnalytics(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/system');
    return response.data;
  }

  async getPerformanceMetrics(): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/performance');
    return response.data;
  }

  async getUsageStatistics(params?: { period?: string; granularity?: string }): Promise<ApiResponse<any>> {
    const response = await this.api.get<ApiResponse<any>>('/analytics/usage', { params });
    return response.data;
  }

  // ============================================================================
  // DOCUMENT FILES API - Enhanced implementation
  // ============================================================================

  async getDocumentFiles(documentId: number): Promise<ApiResponse<DocumentFile[]>> {
    const response = await this.api.get<ApiResponse<DocumentFile[]>>(`/documents/${documentId}/files`);
    return response.data;
  }

  async getDocumentFile(fileId: number): Promise<ApiResponse<DocumentFile>> {
    const response = await this.api.get<ApiResponse<DocumentFile>>(`/files/${fileId}`);
    return response.data;
  }

  async downloadFile(fileId: number): Promise<Blob> {
    const response = await this.api.get(`/files/${fileId}/download`, { responseType: 'blob' });
    return response.data;
  }

  async uploadDocumentFile(documentId: number, file: File, metadata?: any): Promise<ApiResponse<DocumentFile>> {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    const response = await this.api.post<ApiResponse<DocumentFile>>(`/documents/${documentId}/files`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteDocumentFile(documentId: number, fileId: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete<ApiResponse<any>>(`/documents/${documentId}/files/${fileId}`);
    return response.data;
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
