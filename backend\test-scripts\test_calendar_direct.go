package main

import (
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	fmt.Println("=== Testing Calendar API Directly ===")

	// Test the calendar endpoint with the same date range as frontend
	now := time.Now()
	fromDate := "2025-06-30"
	toDate := "2025-07-30"

	url := fmt.Sprintf("http://localhost:8080/api/v1/public/calendar?view=month&from=%s&to=%s&agency_id=&doc_type=&event_type=", fromDate, toDate)

	fmt.Printf("Making request to: %s\n", url)

	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("Error making request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response: %v\n", err)
		return
	}

	fmt.Printf("Status Code: %d\n", resp.StatusCode)
	fmt.Printf("Response Body:\n%s\n", string(body))

	// Also test with today's date specifically
	fmt.Println("\n=== Testing with Today's Date ===")
	today := now.Format("2006-01-02")
	tomorrow := now.AddDate(0, 0, 1).Format("2006-01-02")

	url2 := fmt.Sprintf("http://localhost:8080/api/v1/public/calendar?view=day&from=%s&to=%s", today, tomorrow)
	fmt.Printf("Making request to: %s\n", url2)

	resp2, err := http.Get(url2)
	if err != nil {
		fmt.Printf("Error making request: %v\n", err)
		return
	}
	defer resp2.Body.Close()

	body2, err := io.ReadAll(resp2.Body)
	if err != nil {
		fmt.Printf("Error reading response: %v\n", err)
		return
	}

	fmt.Printf("Status Code: %d\n", resp2.StatusCode)
	fmt.Printf("Response Body:\n%s\n", string(body2))
}
