package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"federal-register-clone/internal/models"
)

func main() {
	// Load environment variables
	if err := godotenv.Load("../.env"); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Get database URL from environment or construct from components
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		// Construct from individual components
		host := os.Getenv("DB_HOST")
		port := os.Getenv("DB_PORT")
		user := os.Getenv("DB_USER")
		password := os.Getenv("DB_PASSWORD")
		dbname := os.Getenv("DB_NAME")
		sslmode := os.Getenv("DB_SSLMODE")

		if host == "" || port == "" || user == "" || password == "" || dbname == "" {
			log.Fatal("Database configuration variables are required")
		}

		if sslmode == "" {
			sslmode = "disable"
		}

		dbURL = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=%s",
			user, password, host, port, dbname, sslmode)
	}

	// Connect to database
	db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("=== Checking Tasks in Database ===")

	// Count all tasks
	var totalTasks int64
	db.Model(&models.Task{}).Count(&totalTasks)
	fmt.Printf("Total tasks in database: %d\n", totalTasks)

	// Count manual tasks
	var manualTasks int64
	db.Model(&models.Task{}).Where("source_type = ?", "manual").Count(&manualTasks)
	fmt.Printf("Manual tasks (source_type = 'manual'): %d\n", manualTasks)

	// Get all tasks with details
	var tasks []models.Task
	db.Find(&tasks)

	fmt.Println("\n=== All Tasks ===")
	for i, task := range tasks {
		fmt.Printf("Task %d:\n", i+1)
		fmt.Printf("  ID: %d\n", task.ID)
		fmt.Printf("  Title: %s\n", task.Title)
		fmt.Printf("  Type: %s\n", task.Type)
		fmt.Printf("  Status: %s\n", task.Status)
		fmt.Printf("  SourceType: %s\n", task.SourceType)
		fmt.Printf("  StartDate: %v\n", task.StartDate)
		fmt.Printf("  EndDate: %v\n", task.EndDate)
		fmt.Printf("  CreatedAt: %v\n", task.CreatedAt)
		fmt.Printf("  IsPublic: %t\n", task.IsPublic)
		fmt.Println("  ---")
	}

	// Get manual tasks specifically
	var manualTasksList []models.Task
	db.Where("source_type = ?", "manual").Find(&manualTasksList)

	fmt.Println("\n=== Manual Tasks Only ===")
	for i, task := range manualTasksList {
		fmt.Printf("Manual Task %d:\n", i+1)
		fmt.Printf("  ID: %d\n", task.ID)
		fmt.Printf("  Title: %s\n", task.Title)
		fmt.Printf("  Type: %s\n", task.Type)
		fmt.Printf("  StartDate: %v\n", task.StartDate)
		fmt.Printf("  EndDate: %v\n", task.EndDate)
		fmt.Printf("  IsPublic: %t\n", task.IsPublic)
		fmt.Println("  ---")
	}
}
