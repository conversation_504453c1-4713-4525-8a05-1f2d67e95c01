{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMIe7T6kIbqWID+9XSG6Sx67cuIQ4yC8v4gkCW7mjhA=", "__NEXT_PREVIEW_MODE_ID": "841018894c788b3692d3106f9b30f328", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3fd5b82dbe35cb84377294465340c89999202c1606f3dd6502082959942f70cd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "328cf0373ad01c59e0f9a5fc8a15e5514a91720e91510a0a54b3516d8ef5554c"}}}, "functions": {}, "sortedMiddleware": ["/"]}