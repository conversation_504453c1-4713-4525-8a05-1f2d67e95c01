{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMIe7T6kIbqWID+9XSG6Sx67cuIQ4yC8v4gkCW7mjhA=", "__NEXT_PREVIEW_MODE_ID": "780d9d7819593635d9d47ab9265a12ff", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ffc57ca034d14fadea8527654fd64654ca62bfdc23ed3520eb1cbd183b0507d7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "741ae285840f651abc88771bc940510462a56eaf724ef3ee5c2dfaebd7ffbacf"}}}, "functions": {}, "sortedMiddleware": ["/"]}