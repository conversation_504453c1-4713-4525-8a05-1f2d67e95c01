package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

const baseURL = "http://localhost:8080"

// Test data structures
type LoginRequest struct {
	Identifier string `json:"identifier"`
	Password   string `json:"password"`
}

type LoginResponse struct {
	Message string `json:"message"`
	Data    struct {
		Token string `json:"token"`
		User  struct {
			ID       uint   `json:"id"`
			Username string `json:"username"`
		} `json:"user"`
	} `json:"data"`
}

type CalendarEventRequest struct {
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Type        string     `json:"type"`
	StartDate   time.Time  `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
	IsAllDay    bool       `json:"is_all_day"`
	Location    string     `json:"location"`
	URL         string     `json:"url"`
	DocumentID  *uint      `json:"document_id"`
	AgencyID    *uint      `json:"agency_id"`
	IsPublic    bool       `json:"is_public"`
}

func main() {
	fmt.Println("=== Calendar Comprehensive Test ===")

	// Step 1: Login to get authentication token
	token, err := login()
	if err != nil {
		log.Fatalf("Login failed: %v", err)
	}
	fmt.Printf("✓ Login successful, token: %s...\n", token[:20])

	// Step 2: Test calendar stats endpoint
	fmt.Println("\n--- Testing Calendar Stats ---")
	if err := testCalendarStats(token); err != nil {
		log.Printf("Calendar stats test failed: %v", err)
	} else {
		fmt.Println("✓ Calendar stats test passed")
	}

	// Step 3: Test calendar endpoint (before creating events)
	fmt.Println("\n--- Testing Calendar (before events) ---")
	if err := testCalendar(token, "before"); err != nil {
		log.Printf("Calendar test (before) failed: %v", err)
	} else {
		fmt.Println("✓ Calendar test (before) passed")
	}

	// Step 4: Create test calendar events
	fmt.Println("\n--- Creating Test Calendar Events ---")
	eventIDs := []string{}

	// Create different types of events
	events := []CalendarEventRequest{
		{
			Title:       "Team Meeting",
			Description: "Weekly team sync meeting",
			Type:        "general",
			StartDate:   time.Now().Add(24 * time.Hour),
			IsAllDay:    false,
			Location:    "Conference Room A",
			IsPublic:    true,
		},
		{
			Title:       "Document Review",
			Description: "Review new regulation document",
			Type:        "review",
			StartDate:   time.Now().Add(48 * time.Hour),
			IsAllDay:    true,
			IsPublic:    true,
		},
		{
			Title:       "Comment Period Reminder",
			Description: "Reminder to submit comments",
			Type:        "reminder",
			StartDate:   time.Now().Add(72 * time.Hour),
			IsAllDay:    false,
			URL:         "https://example.com/comments",
			IsPublic:    true,
		},
		{
			Title:       "Follow-up Task",
			Description: "Follow up on previous meeting",
			Type:        "follow_up",
			StartDate:   time.Now().Add(96 * time.Hour),
			IsAllDay:    false,
			Location:    "Office 123",
			IsPublic:    false,
		},
	}

	for i, event := range events {
		eventID, err := createCalendarEvent(token, event)
		if err != nil {
			log.Printf("Failed to create event %d: %v", i+1, err)
		} else {
			fmt.Printf("✓ Created event %d: %s (ID: %s)\n", i+1, event.Title, eventID)
			eventIDs = append(eventIDs, eventID)
		}
	}

	// Step 5: Test calendar endpoint (after creating events)
	fmt.Println("\n--- Testing Calendar (after events) ---")
	if err := testCalendar(token, "after"); err != nil {
		log.Printf("Calendar test (after) failed: %v", err)
	} else {
		fmt.Println("✓ Calendar test (after) passed")
	}

	// Step 6: Test individual event retrieval
	fmt.Println("\n--- Testing Individual Event Retrieval ---")
	for i, eventID := range eventIDs {
		if err := testGetCalendarEvent(token, eventID); err != nil {
			log.Printf("Failed to get event %d (%s): %v", i+1, eventID, err)
		} else {
			fmt.Printf("✓ Retrieved event %d (%s)\n", i+1, eventID)
		}
	}

	// Step 7: Test event filtering
	fmt.Println("\n--- Testing Event Filtering ---")
	if err := testCalendarFiltering(token); err != nil {
		log.Printf("Calendar filtering test failed: %v", err)
	} else {
		fmt.Println("✓ Calendar filtering test passed")
	}

	fmt.Println("\n=== Calendar Test Complete ===")
}

func login() (string, error) {
	loginReq := LoginRequest{
		Identifier: "<EMAIL>",
		Password:   "password",
	}

	jsonData, err := json.Marshal(loginReq)
	if err != nil {
		return "", err
	}

	resp, err := http.Post(baseURL+"/api/v1/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("login failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", err
	}

	return loginResp.Data.Token, nil
}

func testCalendarStats(token string) error {
	req, err := http.NewRequest("GET", baseURL+"/api/v1/calendar/stats", nil)
	if err != nil {
		return err
	}
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("calendar stats failed with status %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("Calendar stats response: %s\n", string(body))
	return nil
}

func testCalendar(token, phase string) error {
	// Test with date range
	now := time.Now()
	fromDate := now.Format("2006-01-02")
	toDate := now.AddDate(0, 1, 0).Format("2006-01-02")

	url := fmt.Sprintf("%s/api/v1/calendar?from=%s&to=%s", baseURL, fromDate, toDate)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err
	}
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("calendar test failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response to check events
	var calendarData map[string]interface{}
	if err := json.Unmarshal(body, &calendarData); err != nil {
		return err
	}

	events, ok := calendarData["events"].([]interface{})
	if !ok {
		return fmt.Errorf("events field not found or not an array")
	}

	fmt.Printf("Calendar %s: Found %d events\n", phase, len(events))

	// Print event details
	for i, event := range events {
		eventMap, ok := event.(map[string]interface{})
		if !ok {
			continue
		}
		fmt.Printf("  Event %d: %s (Type: %s, ID: %s)\n",
			i+1,
			eventMap["title"],
			eventMap["type"],
			eventMap["id"])
	}

	return nil
}

func createCalendarEvent(token string, event CalendarEventRequest) (string, error) {
	jsonData, err := json.Marshal(event)
	if err != nil {
		return "", err
	}

	req, err := http.NewRequest("POST", baseURL+"/api/v1/calendar/events", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusCreated {
		return "", fmt.Errorf("create event failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}

	data, ok := response["data"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	id, ok := data["id"].(float64)
	if !ok {
		return "", fmt.Errorf("invalid event ID format")
	}

	return fmt.Sprintf("%.0f", id), nil
}

func testGetCalendarEvent(token, eventID string) error {
	req, err := http.NewRequest("GET", baseURL+"/api/v1/calendar/events/"+eventID, nil)
	if err != nil {
		return err
	}
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get event failed with status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

func testCalendarFiltering(token string) error {
	// Test filtering by event type
	eventTypes := []string{"general", "review", "reminder", "follow_up"}

	for _, eventType := range eventTypes {
		url := fmt.Sprintf("%s/api/v1/calendar?event_type=%s", baseURL, eventType)

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			return err
		}
		req.Header.Set("Authorization", "Bearer "+token)

		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return err
		}

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("calendar filtering failed for type %s with status %d: %s", eventType, resp.StatusCode, string(body))
		}

		var calendarData map[string]interface{}
		if err := json.Unmarshal(body, &calendarData); err != nil {
			return err
		}

		events, ok := calendarData["events"].([]interface{})
		if !ok {
			return fmt.Errorf("events field not found for type %s", eventType)
		}

		fmt.Printf("  Filter by type '%s': Found %d events\n", eventType, len(events))
	}

	return nil
}
